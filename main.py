import taichi as ti
from taichi.math import *
import numpy as np

ti.init(arch=ti.gpu, default_ip=ti.i32, default_fp=ti.f32)

image_resolution = (192*15, 108*15)

image_buffer = ti.Vector.field(4, float, image_resolution)
image_pixels = ti.Vector.field(3, float, image_resolution)

# SDF预计算网格参数 - 提高分辨率以获得更平滑的表面
SDF_GRID_SIZE = 512  # 从256提高到512，获得更高精度
SDF_BOUNDS = 4.0  # 网格边界范围
SDF_CELL_SIZE = 2.0 * SDF_BOUNDS / SDF_GRID_SIZE

# 预计算的SDF网格
sdf_grid = ti.field(float, shape=(SDF_GRID_SIZE, SDF_GRID_SIZE, SDF_GRID_SIZE))
sdf_grid_initialized = ti.field(bool, shape=())

# 加载预计算的SDF缓存
def load_sdf_cache():
    """加载预计算的茶壶SDF缓存"""
    try:
        sdf_data = np.load('teapot_sdf_cache.npy')
        sdf_grid.from_numpy(sdf_data)
        sdf_grid_initialized[None] = True
        print(f"成功加载SDF缓存，数据形状: {sdf_data.shape}")
        print(f"数据范围: [{sdf_data.min():.6f}, {sdf_data.max():.6f}]")
        return True
    except FileNotFoundError:
        print("警告: 未找到SDF缓存文件 'teapot_sdf_cache.npy'")
        print("请先运行 preprocess.py 生成缓存")
        sdf_grid_initialized[None] = False
        return False
    except Exception as e:
        print(f"加载SDF缓存时出错: {e}")
        sdf_grid_initialized[None] = False
        return False

SCREEN_PIXEL_SIZE = 1.0 / vec2(image_resolution)
PIXEL_RADIUS      = 0.5 * min(SCREEN_PIXEL_SIZE.x, SCREEN_PIXEL_SIZE.y)

MIN_DIS      = 0.005
MAX_DIS      = 2000.0
VISIBILITY   = 0.000001

SAMPLE_PER_PIXEL = 1
MAX_RAYMARCH = 512
MAX_RAYTRACE = 512

SHAPE_SPHERE   = 1
SHAPE_BOX      = 2
SHAPE_CYLINDER = 3
SHAPE_TEAPOT   = 4

ENV_IOR = 1.000277

aspect_ratio    = image_resolution[0] / image_resolution[1]
light_quality   = 128.0
camera_exposure = 1.0
camera_vfov     = 30
camera_aperture = 0.01
camera_focus    = 4
camera_gamma    = 2.2

@ti.data_oriented
class Image:
    def __init__(self, path: str):
        img = ti.tools.imread(path).astype('float32')
        self.img = vec3.field(shape=img.shape[:2])
        self.img.from_numpy(img / 255)

    @ti.kernel
    def process(self, exposure: float, gamma: float):
        for i, j in self.img:
            color = self.img[i, j] * exposure
            color = pow(color, vec3(gamma))
            self.img[i, j] = color

    @ti.func
    def texture(self, uv: vec2) -> vec3:
        x = int(uv.x * self.img.shape[0])
        y = int(uv.y * self.img.shape[1])
        return self.img[x, y]

hdr_map = Image('assets/Tokyo_BigSight_3k.hdr')
hdr_map.process(exposure=1.8, gamma=camera_gamma)

@ti.dataclass
class Ray:
    origin: vec3
    direction: vec3
    color: vec3

@ti.dataclass
class Material:
    albedo: vec3
    emission: vec3
    roughness: float
    metallic: float
    transmission: float
    ior: float

@ti.dataclass
class Transform:
    position: vec3
    rotation: vec3
    scale: vec3
    matrix: mat3

@ti.dataclass
class SDFObject:
    type: int
    distance: float
    transform: Transform
    material: Material

@ti.dataclass
class Camera:
    lookfrom: vec3
    lookat: vec3
    vup: vec3
    vfov: float
    aspect: float
    aperture: float
    focus: float

OBJECTS_LIST = sorted([
    SDFObject(type=SHAPE_SPHERE,
                transform=Transform(vec3(0, -100.501, 0), vec3(0), vec3(100)),
                material=Material(vec3(1, 1, 1)*0.6, vec3(1), 1, 1, 0, 1.635)),
    SDFObject(type=SHAPE_SPHERE,
                transform=Transform(vec3(0, 0, 0), vec3(0), vec3(0.5)),
                material=Material(vec3(1, 1, 1), vec3(0.1, 1, 0.1)*10, 1, 0, 0, 1)),
    SDFObject(type=SHAPE_SPHERE,
                transform=Transform(vec3(1, -0.2, 0), vec3(0), vec3(0.3)),
                material=Material(vec3(0.2, 0.2, 1), vec3(1), 0.2, 1, 0, 1.100)),
    SDFObject(type=SHAPE_SPHERE,
                transform=Transform(vec3(0.0, -0.2, 2), vec3(0), vec3(0.3)),
                material=Material(vec3(1, 1, 1)*0.9, vec3(1), 0, 0, 1, 1.5)),
    SDFObject(type=SHAPE_CYLINDER,
                transform=Transform(vec3(-1.0, -0.2, 0), vec3(0), vec3(0.3)),
                material=Material(vec3(1.0, 0.2, 0.2), vec3(1), 0, 0, 0, 1.460)),
    SDFObject(type=SHAPE_BOX,
                transform=Transform(vec3(0, 0, 5), vec3(0), vec3(2, 1, 0.2)),
                material=Material(vec3(1, 1, 0.2)*0.9, vec3(1), 0, 1, 0, 0.470)),
    SDFObject(type=SHAPE_BOX,
                transform=Transform(vec3(0, 0, -2), vec3(0), vec3(2, 1, 0.2)),
                material=Material(vec3(1, 1, 1)*0.9, vec3(1), 0, 1, 0, 2.950)),
    SDFObject(type=SHAPE_TEAPOT,
                transform=Transform(vec3(-2, 0, 0), vec3(0), vec3(1)),
                material=Material(vec3(0.8, 0.6, 0.4), vec3(1), 0.3, 0.1, 0, 1.5))
], key=lambda o: o.type)

SHAPE_SPLIT = [0, 0, 0, 0, 0]
for o in OBJECTS_LIST: SHAPE_SPLIT[o.type] += 1
for i in range(1, len(SHAPE_SPLIT)): SHAPE_SPLIT[i] += SHAPE_SPLIT[i - 1]

objects = SDFObject.field()
ti.root.dense(ti.i, len(OBJECTS_LIST)).place(objects)
for i in range(objects.shape[0]): objects[i] = OBJECTS_LIST[i]

@ti.func
def random_in_unit_disk() -> vec2:
    x = ti.random()
    a = ti.random() * 2 * pi
    return sqrt(x) * vec2(sin(a), cos(a))

@ti.func
def get_ray(c: Camera, uv: vec2, color: vec3) -> Ray:
    theta = radians(c.vfov)
    half_height = tan(theta * 0.5)
    half_width = c.aspect * half_height

    z = normalize(c.lookfrom - c.lookat)
    x = normalize(cross(c.vup, z))
    y = cross(z, x)

    lens_radius = c.aperture * 0.5
    rud = lens_radius * random_in_unit_disk()
    offset = x * rud.x + y * rud.y
    
    hwfx = half_width  * c.focus * x
    hhfy = half_height * c.focus * y

    lower_left_corner = c.lookfrom - hwfx - hhfy - c.focus * z
    horizontal = 2.0 * hwfx
    vertical   = 2.0 * hhfy

    ro = c.lookfrom + offset
    po = lower_left_corner + uv.x * horizontal + uv.y * vertical
    rd = normalize(po - ro)

    return Ray(ro, rd, color)

@ti.func
def at(r: Ray, t: float) -> vec3:
    return r.origin + t * r.direction

@ti.func
def angle(a: vec3) -> mat3:
    s, c = sin(a), cos(a)
    return mat3(vec3( c.z,  s.z,    0),
                vec3(-s.z,  c.z,    0),
                vec3(   0,    0,    1)) @ \
           mat3(vec3( c.y,    0, -s.y),
                vec3(   0,    1,    0),
                vec3( s.y,    0,  c.y)) @ \
           mat3(vec3(   1,    0,    0),
                vec3(   0,  c.x,  s.x),
                vec3(   0, -s.x,  c.x))

@ti.func
def sd_sphere(p: vec3, r: vec3) -> float:
    return length(p) - r.x

@ti.func
def sd_box(p: vec3, b: vec3) -> float:
    q = abs(p) - b
    return length(max(q, 0)) + min(max(q.x, max(q.y, q.z)), 0) - 0.03

@ti.func
def sd_cylinder(p: vec3, rh: vec3) -> float:
    d = abs(vec2(length(p.xz), p.y)) - rh.xy
    return min(max(d.x, d.y), 0) + length(max(d, 0))

# ===================================================================
# ===                     茶壶SDF辅助函数                         ===
# ===================================================================

@ti.func
def U_func(a: vec2, b: vec2) -> float:
    return a.x * b.y - b.x * a.y

@ti.func
def B_func(m: vec2, n: vec2, o: vec2, p: vec3) -> vec2:
    q = p.xy
    m -= q
    n -= q
    o -= q
    x = U_func(m, o)
    y = 2.0 * U_func(n, m)
    z = 2.0 * U_func(o, n)
    i = o - m
    j = o - n
    k = n - m
    s = 2.0 * (x * i + y * j + z * k)
    s_dot = dot(s, s)
    r = m
    if s_dot > 1e-8:
        r = m + (y * z - x * x) * vec2(s.y, -s.x) / s_dot

    denom = x + x + y + z
    t = 0.0
    if abs(denom) > 1e-8:
        t = clamp((U_func(r, i) + 2.0 * U_func(k, r)) / denom, 0.0, 1.0)
    r = m + t * (k + k + t * (j - k))
    return vec2(sqrt(dot(r, r) + p.z * p.z), t)

@ti.func
def smin_func(a: float, b: float, k: float) -> float:
    h = clamp(0.5 + 0.5 * (b - a) / k, 0.0, 1.0)
    return mix(b, a, h) - k * h * (1.0 - h)

@ti.func
def dot2_func(v: vec3) -> float:
    return dot(v, v)

@ti.func
def udTriangle_func(v1: vec3, v2: vec3, v3: vec3, p: vec3) -> float:
    v21 = v2 - v1
    p1 = p - v1
    v32 = v3 - v2
    p2 = p - v2
    v13 = v1 - v3
    p3 = p - v3
    nor = cross(v21, v13)

    sign1 = 1.0 if dot(cross(v21, nor), p1) >= 0 else -1.0
    sign2 = 1.0 if dot(cross(v32, nor), p2) >= 0 else -1.0
    sign3 = 1.0 if dot(cross(v13, nor), p3) >= 0 else -1.0

    result = 0.0
    if (sign1 + sign2 + sign3) < 2.0:
        d1 = dot2_func(v21 * clamp(dot(v21, p1) / dot2_func(v21), 0.0, 1.0) - p1)
        d2 = dot2_func(v32 * clamp(dot(v32, p2) / dot2_func(v32), 0.0, 1.0) - p2)
        d3 = dot2_func(v13 * clamp(dot(v13, p3) / dot2_func(v13), 0.0, 1.0) - p3)
        result = sqrt(min(min(d1, d2), d3))
    else:
        result = sqrt(dot(nor, p1) * dot(nor, p1) / dot2_func(nor))

    return result

@ti.func
def rotate3d_func(angle: float) -> mat3:
    return mat3(vec3(1, 0, 0),
                vec3(0, cos(angle), -sin(angle)),
                vec3(0, sin(angle), cos(angle)))

@ti.func
def opCheapBend_func(p: vec3) -> vec3:
    k = 0.1
    c = cos(-k * p.x)
    s = sin(-k * p.x)
    m = mat2(vec2(c, -s), vec2(s, c))
    xz_transform = m @ vec2(p.x, p.z)
    return vec3(xz_transform.x, p.y, xz_transform.y)

# ===================================================================
# ===                     茶壶和字母U的SDF函数                     ===
# ===================================================================

@ti.func
def letterU_func(point: vec3) -> float:
    scale = 0.2
    p = point * scale

    # 构建字母U的各个部分
    box1 = sd_box((point - vec3(0.25, 0.35, 0.0)) * scale, vec3(0.025, 0.05, 0.01))
    box2 = sd_box(p, vec3(0.025, 0.025, 0.01))
    box3 = sd_box((point - vec3(-0.25, 0.35, 0.0)) * scale, vec3(0.025, 0.05, 0.01))

    tri1 = udTriangle_func(vec3(0.02, 0.025, 0.01), vec3(0.075, 0.02, 0.01), vec3(0.02, -0.027, 0.01), p)
    tri2 = udTriangle_func(vec3(-0.02, 0.025, 0.01), vec3(-0.075, 0.02, 0.01), vec3(-0.02, -0.027, 0.01), p)

    box4 = sd_box((point - vec3(0.265, 0.68, 0.0)) * scale, vec3(0.038, 0.025, 0.01))
    box5 = sd_box((point - vec3(-0.265, 0.68, 0.0)) * scale, vec3(0.038, 0.025, 0.01))

    return min(min(min(min(min(min(box1, box2), box3), tri1), tri2), box4), box5)

@ti.func
def teapot_M_func(p: vec3) -> float:
    p.y -= -0.5

    # 定义茶壶的静态控制点
    A0 = vec2(0, 0); A1 = vec2(0.64, 0); A2 = vec2(0.64, 0.03)
    A3 = vec2(0.8, 0.12); A4 = vec2(0.8, 0.3); A5 = vec2(0.8, 0.48)
    A6 = vec2(0.64, 0.9); A7 = vec2(0.6, 0.93)

    T0 = vec2(0.56, 0.9); T1 = vec2(0.56, 0.96); T2 = vec2(0.12, 1.02)
    T3 = vec2(0, 1.05); T4 = vec2(0.16, 1.14); T5 = vec2(0.2, 1.2)
    T6 = vec2(0, 1.2)

    T1_0 = vec2(1.16, 0.96); T1_1 = vec2(1.04, 0.9); T1_2 = vec2(1, 0.72)
    T1_3 = vec2(0.92, 0.48); T1_4 = vec2(0.72, 0.42)

    T2_0 = vec2(-0.6, 0.78); T2_1 = vec2(-1.16, 0.84); T2_2 = vec2(-1.16, 0.63)
    T2_3 = vec2(-1.2, 0.42); T2_4 = vec2(-0.72, 0.24)

    h = B_func(T1_2, T1_3, T1_4, p)
    a = 99.0
    r = length(p)

    b1 = min(B_func(T2_0, T2_1, T2_2, p).x, B_func(T2_2, T2_3, T2_4, p).x) - 0.06
    b2 = max(p.y - 0.9, min(abs(B_func(T1_0, T1_1, T1_2, p).x - 0.07) - 0.01, h.x * (1.0 - 0.75 * h.y) - 0.08))
    b = min(b1, b2)

    qq = vec3(r * sin(acos(p.y / r)), p.y, 0)

    # 计算A和T的贝塞尔曲线距离
    a = min(a, (B_func(A0, A1, A2, qq).x - 0.015) * 0.8)
    a = min(a, (B_func(A2, A3, A4, qq).x - 0.015) * 0.8)
    a = min(a, (B_func(A4, A5, A6, qq).x - 0.015) * 0.8)

    a = min(a, (B_func(T0, T1, T2, qq).x - 0.015) * 0.8)
    a = min(a, (B_func(T2, T3, T4, qq).x - 0.015) * 0.8)
    a = min(a, (B_func(T4, T5, T6, qq).x - 0.015) * 0.8)

    return smin_func(a, b, 0.02)

@ti.func
def smoothstep(t: float) -> float:
    """平滑插值函数，比线性插值更平滑"""
    return t * t * (3.0 - 2.0 * t)

@ti.func
def sample_sdf_cache(p: vec3) -> float:
    """从预计算的SDF缓存中采样，使用平滑插值"""
    # 将世界坐标转换为网格坐标
    grid_pos = p / SDF_CELL_SIZE + SDF_GRID_SIZE * 0.5

    result = 0.0

    # 边界检查
    if (grid_pos.x < 0 or grid_pos.x >= SDF_GRID_SIZE or
        grid_pos.y < 0 or grid_pos.y >= SDF_GRID_SIZE or
        grid_pos.z < 0 or grid_pos.z >= SDF_GRID_SIZE):
        result = MAX_DIS  # 超出边界返回最大距离
    else:
        # 三线性插值采样，使用平滑插值
        i = int(grid_pos.x)
        j = int(grid_pos.y)
        k = int(grid_pos.z)

        # 确保索引在有效范围内
        i = max(0, min(i, SDF_GRID_SIZE - 2))
        j = max(0, min(j, SDF_GRID_SIZE - 2))
        k = max(0, min(k, SDF_GRID_SIZE - 2))

        # 计算插值权重，使用平滑插值函数
        fx = smoothstep(grid_pos.x - i)
        fy = smoothstep(grid_pos.y - j)
        fz = smoothstep(grid_pos.z - k)

        # 三线性插值
        c000 = sdf_grid[i, j, k]
        c001 = sdf_grid[i, j, k + 1]
        c010 = sdf_grid[i, j + 1, k]
        c011 = sdf_grid[i, j + 1, k + 1]
        c100 = sdf_grid[i + 1, j, k]
        c101 = sdf_grid[i + 1, j, k + 1]
        c110 = sdf_grid[i + 1, j + 1, k]
        c111 = sdf_grid[i + 1, j + 1, k + 1]

        c00 = c000 * (1 - fx) + c100 * fx
        c01 = c001 * (1 - fx) + c101 * fx
        c10 = c010 * (1 - fx) + c110 * fx
        c11 = c011 * (1 - fx) + c111 * fx

        c0 = c00 * (1 - fy) + c10 * fy
        c1 = c01 * (1 - fy) + c11 * fy

        result = c0 * (1 - fz) + c1 * fz

    return result

@ti.func
def sd_teapot(p: vec3, scale: vec3) -> float:
    # 使用缓存或实时计算
    result = 0.0

    # 如果SDF缓存已初始化，使用缓存
    if sdf_grid_initialized[None]:
        result = sample_sdf_cache(p)
    else:
        # 否则使用原始计算方法
        # 1. 计算到茶壶的距离
        teapot = teapot_M_func(p * 0.65)

        # 2. 计算到字母'U'的距离 (应用了变形，但移除了动画位移)
        u_point = rotate3d_func(-0.27) @ opCheapBend_func(p) + vec3(0.0, 0.6, -1.11)
        u_shape = letterU_func(u_point)

        # 3. 返回两个形状的最小距离 (Union操作)
        result = min(teapot, u_shape)

    return result

@ti.func
def transform(t: Transform, p: vec3) -> vec3:
    p -= t.position # Cannot squeeze the Euclidean space of distance field
    p  = t.matrix @ p # Otherwise the correct ray marching is not possible
    return p

@ti.func
def signed_distance(obj: SDFObject, pos: vec3) -> float:
    scale = obj.transform.scale
    p = transform(obj.transform, pos)

    if    obj.type == SHAPE_SPHERE:   obj.distance = sd_sphere(p, scale)
    elif  obj.type == SHAPE_BOX:      obj.distance = sd_box(p, scale)
    elif  obj.type == SHAPE_CYLINDER: obj.distance = sd_cylinder(p, scale)
    elif  obj.type == SHAPE_TEAPOT:   obj.distance = sd_teapot(p, scale)
    else:                             obj.distance = MAX_DIS

    return obj.distance

@ti.func
def get_object_pos_scale(i: int, p: vec3):
    obj = objects[i]
    pos = transform(obj.transform, p)
    return pos, obj.transform.scale

@ti.func
def nearest_object(p: vec3):
    index = 0; min_dis = MAX_DIS
    for i in ti.static(range(SHAPE_SPLIT[0], SHAPE_SPLIT[1])):
        pos, scale = get_object_pos_scale(i, p)
        dis = abs(sd_sphere(pos, scale))
        if dis < min_dis: min_dis = dis; index = i
    for i in ti.static(range(SHAPE_SPLIT[1], SHAPE_SPLIT[2])):
        pos, scale = get_object_pos_scale(i, p)
        dis = abs(sd_box(pos, scale))
        if dis < min_dis: min_dis = dis; index = i
    for i in ti.static(range(SHAPE_SPLIT[2], SHAPE_SPLIT[3])):
        pos, scale = get_object_pos_scale(i, p)
        dis = abs(sd_cylinder(pos, scale))
        if dis < min_dis: min_dis = dis; index = i
    for i in ti.static(range(SHAPE_SPLIT[3], SHAPE_SPLIT[4])):
        pos, scale = get_object_pos_scale(i, p)
        dis = abs(sd_teapot(pos, scale))
        if dis < min_dis: min_dis = dis; index = i
    return index, min_dis

@ti.func
def calc_normal(obj: SDFObject, p: vec3) -> vec3:
    e = vec2(1, -1) * 0.5773 * 0.005
    return normalize(e.xyy * signed_distance(obj, p + e.xyy) + \
                     e.yyx * signed_distance(obj, p + e.yyx) + \
                     e.yxy * signed_distance(obj, p + e.yxy) + \
                     e.xxx * signed_distance(obj, p + e.xxx) )

@ti.func
def raycast(ray: Ray):
    t = MIN_DIS; w, s, d, cerr = 1.6, 0.0, 0.0, 1e32
    index = 0; position = vec3(0); hit = False
    for _ in range(MAX_RAYMARCH):
        position = at(ray, t)
        index, distance = nearest_object(position)

        ld = d; d = distance
        if ld + d < s:
            s -= w * s; t += s; w = 0.5 + 0.5 * w
            continue
        err = d / t
        if err < cerr: cerr = err

        s = w * d; t += s
        hit = err < PIXEL_RADIUS
        if t > MAX_DIS or hit: break

    return objects[index], position, hit

@ti.func
def sample_spherical_map(v: vec3) -> vec2:
    uv  = vec2(atan2(v.z, v.x), asin(v.y))
    uv *= vec2(0.5 / pi, 1 / pi)
    uv += 0.5
    return uv

@ti.func
def sky_color(ray: Ray) -> vec3:
    uv = sample_spherical_map(ray.direction)
    return hdr_map.texture(uv)

@ti.func
def fresnel_schlick(NoI: float, F0: float, roughness: float) -> float:
    return mix(mix(pow(abs(1.0 + NoI), 5.0), 1.0, F0), F0, roughness)

@ti.func
def hemispheric_sampling(normal: vec3) -> vec3:
    z = 2.0 * ti.random() - 1.0
    a = ti.random() * 2.0 * pi
    
    xy = sqrt(1.0 - z*z) * vec2(sin(a), cos(a))
    
    return normalize(normal + vec3(xy, z))

@ti.func
def roughness_sampling(hemispheric_sample: vec3, normal: vec3, roughness: float) -> vec3:
    alpha = roughness * roughness
    return normalize(mix(normal, hemispheric_sample, alpha))

@ti.func
def ray_surface_interaction(ray: Ray, object: SDFObject, position: vec3) -> Ray:
    albedo       = object.material.albedo
    roughness    = object.material.roughness
    metallic     = object.material.metallic
    transmission = object.material.transmission
    ior          = object.material.ior
    
    normal  = calc_normal(object, position)
    outer   = dot(ray.direction, normal) < 0
    normal *= 1 if outer else -1
    
    hemispheric_sample = hemispheric_sampling(normal)
    roughness_sample   = roughness_sampling(hemispheric_sample, normal, roughness)
    
    N   = roughness_sample
    I   = ray.direction
    NoI = dot(N, I)

    eta = ENV_IOR / ior if outer else ior / ENV_IOR
    k   = 1.0 - eta * eta * (1.0 - NoI * NoI)
    F0  = 2.0 * (eta - 1.0) / (eta + 1.0); F0 *= F0
    F   = fresnel_schlick(NoI, F0, roughness)

    if ti.random() < F + metallic or k < 0.0:
        ray.direction = I - 2.0 * NoI * N
        ray.color *= float(dot(ray.direction, normal) > 0.0)
    elif ti.random() < transmission:
        ray.direction = eta * I - (sqrt(k) + eta * NoI) * N
    else:
        ray.direction = hemispheric_sample

    ray.color *= albedo
    ray.origin = position
    
    return ray

@ti.func
def brightness(rgb: vec3) -> float:
    return dot(rgb, vec3(0.299, 0.587, 0.114))

@ti.func
def raytrace(ray: Ray) -> Ray:
    for i in range(MAX_RAYTRACE):
        inv_pdf = exp(float(i) / light_quality)
        roulette_prob = 1.0 - (1.0 / inv_pdf)
        
        if ti.random() < roulette_prob:
            ray.color *= roulette_prob
            break

        object, position, hit = raycast(ray)

        if not hit:
            ray.color *= sky_color(ray)
            break

        ray = ray_surface_interaction(ray, object, position)

        intensity  = brightness(ray.color)
        ray.color *= object.material.emission
        visible    = brightness(ray.color)

        if intensity < visible or visible < VISIBILITY: break

    return ray

ACESInputMat = mat3(
    0.59719, 0.35458, 0.04823,
    0.07600, 0.90834, 0.01566,
    0.02840, 0.13383, 0.83777
)

ACESOutputMat = mat3(
    +1.60475, -0.53108, -0.07367,
    -0.10208, +1.10813, -0.00605,
    -0.00327, -0.07276, +1.07602
)

@ti.func
def RRTAndODTFit(v: vec3) -> vec3:
    a = v * (v + 0.0245786) - 0.000090537
    b = v * (0.983729 * v + 0.4329510) + 0.238081
    return a / b

@ti.func
def ACESFitted(color: vec3) -> vec3:
    color = ACESInputMat  @ color
    color = RRTAndODTFit(color)
    color = ACESOutputMat @ color
    return color

@ti.func
def update_transform(i: int):
    transform = objects[i].transform
    matrix = angle(radians(transform.rotation))
    objects[i].transform.matrix = matrix

@ti.func
def update_all_transform():
    for i in range(objects.shape[0]):
        update_transform(i)

@ti.kernel
def init_scene():
    update_all_transform()

@ti.kernel
def sample(
    camera_position: vec3, 
    camera_lookat: vec3, 
    camera_up: vec3):

    camera = Camera()
    camera.lookfrom = camera_position
    camera.lookat   = camera_lookat
    camera.vup      = camera_up
    camera.aspect   = aspect_ratio
    camera.vfov     = camera_vfov
    camera.aperture = camera_aperture
    camera.focus    = camera_focus

    for i, j in image_pixels:
        coord = vec2(i, j) + vec2(ti.random(), ti.random())
        uv = coord * SCREEN_PIXEL_SIZE

        ray = raytrace(get_ray(camera, uv, vec3(1)))
        image_buffer[i, j] += vec4(ray.color, 1.0)

@ti.kernel
def refresh():
    image_buffer.fill(vec4(0))

@ti.kernel
def render():
    for i, j in image_pixels:
        buffer = image_buffer[i, j]
        
        color  = buffer.rgb / buffer.a
        color *= camera_exposure
        color  = ACESFitted(color)
        color  = pow(color, vec3(1.0 / camera_gamma))

        image_pixels[i, j] = clamp(color, 0, 1)

window = ti.ui.Window("Taichi Renderer", image_resolution)
canvas = window.get_canvas()
camera = ti.ui.Camera()
camera.position(0, -0.2, 4)

# 加载SDF缓存
print("正在加载茶壶SDF缓存...")
cache_loaded = load_sdf_cache()
if cache_loaded:
    print("SDF缓存加载成功，将使用预计算的茶壶SDF")
else:
    print("SDF缓存加载失败，将使用实时计算的茶壶SDF")

init_scene(); frame = 0
while window.running:
    camera.track_user_inputs(window, movement_speed=0.03, hold_key=ti.ui.LMB)
    moving = any([window.is_pressed(key) for key in ('w', 'a', 's', 'd', 'q', 'e', 'LMB', ' ')])
    if moving: refresh()

    for i in range(SAMPLE_PER_PIXEL):
        sample(
            camera.curr_position, 
            camera.curr_lookat, 
            camera.curr_up)
        print('frame:', frame, 'sample:', i + 1)
    frame += 1
    render()
    
    canvas.set_image(image_pixels)
    window.show()