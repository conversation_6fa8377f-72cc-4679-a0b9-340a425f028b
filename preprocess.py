"""
静态场景SDF预处理器
基于GLSL参考实现，生成茶壶+字母U组合的静态场景SDF
参考GLSL代码中的StaticSceneSDF函数实现
"""
import taichi as ti
import numpy as np
from taichi.math import *

ti.init(arch=ti.gpu)  # 使用GPU进行预计算

# SDF网格参数 - 提高分辨率以获得更平滑的表面
SDF_GRID_SIZE = 512  # 从256提高到512，获得更高精度
SDF_BOUNDS = 4.0
SDF_CELL_SIZE = 2.0 * SDF_BOUNDS / SDF_GRID_SIZE

# 茶壶SDF辅助函数
@ti.func
def U_func(a: vec2, b: vec2) -> float:
    return a.x * b.y - b.x * a.y

@ti.func
def B_func(m: vec2, n: vec2, o: vec2, p: vec3) -> vec2:
    q = p.xy
    m -= q
    n -= q
    o -= q
    x = U_func(m, o)
    y = 2.0 * U_func(n, m)
    z = 2.0 * U_func(o, n)
    i = o - m
    j = o - n
    k = n - m
    s = 2.0 * (x * i + y * j + z * k)
    s_dot = dot(s, s)
    r = m
    if s_dot > 1e-8:
        r = m + (y * z - x * x) * vec2(s.y, -s.x) / s_dot

    denom = x + x + y + z
    t = 0.0
    if abs(denom) > 1e-8:
        t = clamp((U_func(r, i) + 2.0 * U_func(k, r)) / denom, 0.0, 1.0)
    r = m + t * (k + k + t * (j - k))
    return vec2(sqrt(dot(r, r) + p.z * p.z), t)

@ti.func
def smin_func(a: float, b: float, k: float) -> float:
    h = clamp(0.5 + 0.5 * (b - a) / k, 0.0, 1.0)
    return mix(b, a, h) - k * h * (1.0 - h)

@ti.func
def dot2_func(v: vec3) -> float:
    return dot(v, v)

@ti.func
def udTriangle_func(v1: vec3, v2: vec3, v3: vec3, p: vec3) -> float:
    v21 = v2 - v1
    p1 = p - v1
    v32 = v3 - v2
    p2 = p - v2
    v13 = v1 - v3
    p3 = p - v3
    nor = cross(v21, v13)

    sign1 = 1.0 if dot(cross(v21, nor), p1) >= 0 else -1.0
    sign2 = 1.0 if dot(cross(v32, nor), p2) >= 0 else -1.0
    sign3 = 1.0 if dot(cross(v13, nor), p3) >= 0 else -1.0

    result = 0.0
    if (sign1 + sign2 + sign3) < 2.0:
        d1 = dot2_func(v21 * clamp(dot(v21, p1) / dot2_func(v21), 0.0, 1.0) - p1)
        d2 = dot2_func(v32 * clamp(dot(v32, p2) / dot2_func(v32), 0.0, 1.0) - p2)
        d3 = dot2_func(v13 * clamp(dot(v13, p3) / dot2_func(v13), 0.0, 1.0) - p3)
        result = sqrt(min(min(d1, d2), d3))
    else:
        result = sqrt(dot(nor, p1) * dot(nor, p1) / dot2_func(nor))

    return result

@ti.func
def rotate3d_func(angle: float) -> mat3:
    return mat3(vec3(1, 0, 0),
                vec3(0, cos(angle), -sin(angle)),
                vec3(0, sin(angle), cos(angle)))

@ti.func
def opCheapBend_func(p: vec3) -> vec3:
    k = 0.1
    c = cos(-k * p.x)
    s = sin(-k * p.x)
    m = mat2(vec2(c, -s), vec2(s, c))
    xz_transform = m @ vec2(p.x, p.z)
    return vec3(xz_transform.x, p.y, xz_transform.y)

# 添加sd_box函数（从main.py复制）
@ti.func
def sd_box(p: vec3, b: vec3) -> float:
    q = abs(p) - b
    return length(max(q, 0)) + min(max(q.x, max(q.y, q.z)), 0) - 0.03

# 字母U的SDF函数（完全从main.py复制）
@ti.func
def letterU_func(point: vec3) -> float:
    scale = 0.2
    p = point * scale

    # 构建字母U的各个部分
    box1 = sd_box((point - vec3(0.25, 0.35, 0.0)) * scale, vec3(0.025, 0.05, 0.01))
    box2 = sd_box(p, vec3(0.025, 0.025, 0.01))
    box3 = sd_box((point - vec3(-0.25, 0.35, 0.0)) * scale, vec3(0.025, 0.05, 0.01))

    tri1 = udTriangle_func(vec3(0.02, 0.025, 0.01), vec3(0.075, 0.02, 0.01), vec3(0.02, -0.027, 0.01), p)
    tri2 = udTriangle_func(vec3(-0.02, 0.025, 0.01), vec3(-0.075, 0.02, 0.01), vec3(-0.02, -0.027, 0.01), p)

    box4 = sd_box((point - vec3(0.265, 0.68, 0.0)) * scale, vec3(0.038, 0.025, 0.01))
    box5 = sd_box((point - vec3(-0.265, 0.68, 0.0)) * scale, vec3(0.038, 0.025, 0.01))

    return min(min(min(min(min(min(box1, box2), box3), tri1), tri2), box4), box5)

# 茶壶SDF函数 - 更新以匹配GLSL参考代码
@ti.func
def teapot_M_func(p: vec3) -> float:
    p.y -= -0.5

    # 定义茶壶的静态控制点 (与GLSL版本完全一致)
    A0 = vec2(0, 0); A1 = vec2(0.64, 0); A2 = vec2(0.64, 0.03)
    A3 = vec2(0.8, 0.12); A4 = vec2(0.8, 0.3); A5 = vec2(0.8, 0.48)
    A6 = vec2(0.64, 0.9); A7 = vec2(0.6, 0.93)

    T0 = vec2(0.56, 0.9); T1 = vec2(0.56, 0.96); T2 = vec2(0.12, 1.02)
    T3 = vec2(0, 1.05); T4 = vec2(0.16, 1.14); T5 = vec2(0.2, 1.2)
    T6 = vec2(0, 1.2)

    T1_0 = vec2(1.16, 0.96); T1_1 = vec2(1.04, 0.9); T1_2 = vec2(1, 0.72)
    T1_3 = vec2(0.92, 0.48); T1_4 = vec2(0.72, 0.42)

    T2_0 = vec2(-0.6, 0.78); T2_1 = vec2(-1.16, 0.84); T2_2 = vec2(-1.16, 0.63)
    T2_3 = vec2(-1.2, 0.42); T2_4 = vec2(-0.72, 0.24)

    h = B_func(T1_2, T1_3, T1_4, p)
    a = 99.0
    r = length(p)

    b1 = min(B_func(T2_0, T2_1, T2_2, p).x, B_func(T2_2, T2_3, T2_4, p).x) - 0.06
    b2 = max(p.y - 0.9, min(abs(B_func(T1_0, T1_1, T1_2, p).x - 0.07) - 0.01, h.x * (1.0 - 0.75 * h.y) - 0.08))
    b = min(b1, b2)

    qq = vec3(r * sin(acos(p.y / r)), p.y, 0)

    # 使用循环结构计算A和T的贝塞尔曲线距离 (匹配GLSL的循环逻辑)
    # 处理A数组 (0,2,4,6 索引对应 i=0,2,4,6)
    a = min(a, (B_func(A0, A1, A2, qq).x - 0.015) * 0.8)
    a = min(a, (B_func(A2, A3, A4, qq).x - 0.015) * 0.8)
    a = min(a, (B_func(A4, A5, A6, qq).x - 0.015) * 0.8)

    # 处理T数组 (0,2,4,6 索引对应 i=0,2,4,6)
    a = min(a, (B_func(T0, T1, T2, qq).x - 0.015) * 0.8)
    a = min(a, (B_func(T2, T3, T4, qq).x - 0.015) * 0.8)
    a = min(a, (B_func(T4, T5, T6, qq).x - 0.015) * 0.8)

    return smin_func(a, b, 0.02)

# 静态场景SDF函数 - 主入口函数 (匹配GLSL的StaticSceneSDF)
@ti.func
def static_scene_sdf(point: vec3) -> float:
    # 1. 计算到茶壶的距离
    teapot = teapot_M_func(point * 0.65)

    # 2. 计算到字母'U'的距离 (应用了变形，但移除了动画位移)
    u_point = rotate3d_func(-0.27) @ opCheapBend_func(point) + vec3(0.0, 0.6, -1.11)
    u_shape = letterU_func(u_point)

    # 3. 返回两个形状的最小距离 (Union操作)
    return min(teapot, u_shape)

# 保持向后兼容的别名
@ti.func
def sd_teapot(p: vec3) -> float:
    return static_scene_sdf(p)

# 预计算SDF网格
sdf_grid = ti.field(float, shape=(SDF_GRID_SIZE, SDF_GRID_SIZE, SDF_GRID_SIZE))

@ti.kernel
def precompute_static_scene_sdf():
    print("开始预计算静态场景SDF...")
    for i, j, k in sdf_grid:
        # 将网格坐标转换为世界坐标
        x = (i - SDF_GRID_SIZE // 2) * SDF_CELL_SIZE
        y = (j - SDF_GRID_SIZE // 2) * SDF_CELL_SIZE
        z = (k - SDF_GRID_SIZE // 2) * SDF_CELL_SIZE

        pos = vec3(x, y, z)

        # 计算静态场景SDF (茶壶+字母U组合)
        sdf_grid[i, j, k] = static_scene_sdf(pos)

    print("静态场景SDF预计算完成!")

def main():
    print("开始预计算静态场景SDF网格 (茶壶+字母U组合)...")
    print(f"网格大小: {SDF_GRID_SIZE}x{SDF_GRID_SIZE}x{SDF_GRID_SIZE}")
    print(f"边界范围: ±{SDF_BOUNDS}")
    print(f"单元格大小: {SDF_CELL_SIZE:.6f}")

    # 执行预计算
    precompute_static_scene_sdf()

    # 保存到文件
    sdf_data = sdf_grid.to_numpy()
    np.save('static_scene_sdf_cache.npy', sdf_data)

    print("静态场景SDF数据已保存到 static_scene_sdf_cache.npy")
    print(f"数据形状: {sdf_data.shape}")
    print(f"数据范围: [{sdf_data.min():.6f}, {sdf_data.max():.6f}]")

    # 显示一些统计信息
    print(f"正值点数: {np.sum(sdf_data > 0)}")
    print(f"负值点数: {np.sum(sdf_data < 0)}")
    print(f"零值点数: {np.sum(sdf_data == 0)}")

if __name__ == "__main__":
    main()
